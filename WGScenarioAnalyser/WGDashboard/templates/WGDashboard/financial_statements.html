{% extends 'WGDashboard/base.html' %}
{% load static %}

{% block content %}

    <!-- Include Navigation Tabs -->
    {% include 'WGDashboard/navigation.html' %}

    <!-- Main Content Section -->
    <section class="main-content text-center" style="margin-top: 25px; margin-left: 20px; margin-right: 20px;">
        {% block dynamic_content %}
            <div class="container-fluid mt-3 custom-container">
                <div class="row d-flex justify-content-center align-items-center">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card text-white bg-dark mb-2 card-full" style="height: 255px;">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <span id="companyName" style="visibility: hidden;">Placeholder</span>
                                                <span id="reportingPeriodAndRating" class="ml-auto" style="margin-right: 34px; visibility: hidden;">Placeholder</span>
                                            </div>
                                            <div class="card-header-line"></div>
                                            <div class="card-body">
                                                <div class="gauge-wrapper">
                                                    <div class="gauge-container">
                                                        <div class="gaugeMeter" id="companyRating"></div>
                                                        <div class="gaugeMeter" id="companyRatingConfidence" data-size="150" data-color="#52A0FF"
                                                            data-back="#00000000" data-label="Rating|Confidence"
                                                            data-label_color="var(--text-color1)" data-percent="75"
                                                            data-stripe="3" data-width="12"
                                                            data-animate_text_colors="1"
                                                            data-text="75%" data-text_size="0.24">
                                                        </div>
                                                            <div class="gaugeMeter" id="companyRatingPlusMinusConfidence" data-size="150" data-color="#52A0FF"
                                                            data-back="#00000000" data-label="+/-1 Rating|Confidence"
                                                            data-label_color="var(--text-color1)" data-percent="89"
                                                            data-stripe="3" data-width="12"
                                                            data-animate_text_colors="1"
                                                            data-text="89%" data-text_size="0.24">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="progress-container">
                                                    <div class="progress-title">
                                                        <h3>Rating Distribution</h3>
                                                    </div>
                                                    <div class="progress">
                                                        <div class="progress-bar progress-bar-downgrade" role="progressbar" aria-label="Segment downgrade" aria-valuenow="12" aria-valuemin="0" aria-valuemax="100" style="width: 12%;">
                                                            <span class="progress-label">12%</span>
                                                        </div>
                                                        <div class="progress-bar progress-bar-rating" role="progressbar" aria-label="Segment rating" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100" style="width: 75%;">
                                                            <span class="progress-label">75%</span>
                                                        </div>
                                                        <div class="progress-bar progress-bar-upgrade" role="progressbar" aria-label="Segment upgrade" aria-valuenow="13" aria-valuemin="0" aria-valuemax="100" style="width: 13%;">
                                                            <span class="progress-label">13%</span>
                                                        </div>
                                                    </div>
                                                    <div class="progress-legend">
                                                        <div class="legend-item">
                                                            <span class="legend-color" style="background-color: var(--third-color);"></span>
                                                            <span class="legend-text">Downgrade</span>
                                                        </div>
                                                        <div class="legend-item">
                                                            <span class="legend-color" style="background-color: var(--main-color);"></span>
                                                            <span class="legend-text">WarGame Rating</span>
                                                        </div>
                                                        <div class="legend-item">
                                                            <span class="legend-color" style="background-color: var(--second-color);"></span>
                                                            <span class="legend-text">Upgrade</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card text-white bg-dark mb-2 card-full" style="height: 255px;">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <span id="financialMetricsCharts" style="visibility: hidden;">Placeholder</span>
                                            </div>
                                            <div class="card-header-line"></div>
                                            <div class="card-body">
                                                 <div id="Financial Metrics Analysis">
                                                    <div class="row justify-content-center mb-1">
                                                        <!-- Column Titles -->
                                                        <div class="col-md-4 d-flex justify-content-center align-items-center">
                                                            <h5 class="text-center" style="color: var(--text-color1); font-size: 0.75em; margin-top: 10px; margin-bottom: 0px;">Liquidity</h5>
                                                        </div>
                                                        <div class="col-md-4 d-flex justify-content-center align-items-center">
                                                            <h5 class="text-center" style="color: var(--text-color1); font-size: 0.75em; margin-top: 10px; margin-bottom: 0px;">Profitability / Efficiency</h5>
                                                        </div>
                                                        <div class="col-md-4 d-flex justify-content-center align-items-center">
                                                            <h5 class="text-center" style="color: var(--text-color1); font-size: 0.75em; margin-top: 10px; margin-bottom: 0px;">Leverage / Capital Structure</h5>
                                                        </div>
                                                    </div>
                                                    <div class="row justify-content-center mb-2">
                                                        <div class="col-md-4 d-flex justify-content-center align-items-center">
                                                            <div id="liquidityRadar" style="margin-top: 0px; margin-bottom: -5px;"></div>
                                                        </div>
                                                        <div class="col-md-4 d-flex justify-content-center align-items-center">
                                                            <div id="profitabilityRadar" style="margin-top: 0px; margin-bottom: -5px;"></div>
                                                        </div>
                                                        <div class="col-md-4 d-flex justify-content-center align-items-center">
                                                            <div id="leverageRadar" style="margin-top: 0px; margin-bottom: -5px;"></div>
                                                        </div>
                                                    </div>
                                                    <div class="progress-legend-financial-metrics">
                                                        <div class="legend-item-financial-metrics">
                                                            <span class="legend-color" style="background-color: var(--main-color);"></span>
                                                            <span class="legend-text">Company</span>
                                                        </div>
                                                        <div class="legend-item-financial-metrics">
                                                            <span class="legend-color" style="background-color: var(--second-color);"></span>
                                                            <span class="legend-text">Sector Median</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <!-- Left Column with Two Cards -->
                                    <div class="col-md-6 d-flex flex-column">
                                        <!-- Card for Line Chart -->
                                        <div class="card text-white bg-dark mb-2 card-full flex-grow-1" style="height: 255px;">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <span>Rating History</span>
                                            </div>
                                            <div class="card-header-line"></div>
                                            <div class="card-body">
                                                <div id="RatingChart">
                                                    <canvas id="RatingHistory" style="width: 100%; height: 100%;"></canvas>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Card for CDS Spread Term Structure -->
                                        <div class="card text-white bg-dark mb-2 card-full flex-grow-1" style="height: 255px;">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <span>CDS Spread Term Structure</span>

                                            </div>
                                            <div class="card-header-line"></div>
                                            <div class="card-body">
                                                <div id="SpreadChart">
                                                    <canvas id="SpreadTermStructure" style="width: 100%; height: 100%;"></canvas>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Right Column with Single Card -->
                                    <div class="col-md-6">
                                        <div class="card text-white bg-dark mb-2 card-full" style="height: 518px;">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <span id="financialMetrics" style="visibility: hidden;">Placeholder</span>
                                            </div>
                                            <div class="card-header-line"></div>
                                            <div class="card-body">
                                                <div id="Financial Metrics">
                                                <!-- Table 1: Liquidity Metrics -->
                                                    <table class="financial-metrics-table">
                                                        <thead>
                                                            <tr>
                                                                <th>Liquidity</th>
                                                                <th>Company</th>
                                                                <th>Sector Median</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="liquidityMetricsBody"></tbody>
                                                    </table>
                                                    <!-- Table 2: Profitability / Efficiency Metrics -->
                                                    <table class="financial-metrics-table">
                                                        <thead>
                                                            <tr>
                                                                <th>Profitability / Efficiency</th>
                                                                <th>Company</th>
                                                                <th>Sector Median</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="profitabilityMetricsBody"></tbody>
                                                    </table>
                                                    <!-- Table 3: Leverage Metrics -->
                                                    <table class="financial-metrics-table">
                                                        <thead>
                                                            <tr>
                                                                <th>Leverage / Capital Structure</th>
                                                                <th>Company</th>
                                                                <th>Sector Median</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="leverageMetricsBody"></tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-white bg-dark mb-2 card-full" style="height: 780px;">
                                    <div class="card-body">
                                        <div id="Financial Statements">
                                            <div class="row position-relative">
                                                <!-- First row of tables -->
                                                <div class="col-md-6 d-flex justify-content-center align-items-start p-0">
                                                    <table id="IncomeTable" class="income-table">
                                                        <thead>
                                                            <tr><th>Income (USD Millions)</th><th>Company</th></tr>
                                                        </thead>
                                                        <tbody id="incomeBody"></tbody>
                                                    </table>
                                                </div>
                                                <div class="col-md-6 d-flex justify-content-center align-items-start p-0 position-relative">
                                                    <table id="CashFlowTable" class="cashflow-table">
                                                        <thead>
                                                            <tr><th>Cash Flow (USD Millions)</th><th>Company</th></tr>
                                                        </thead>
                                                        <tbody id="cashflowBody"></tbody>
                                                    </table>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <!-- Second row of tables -->
                                                <div class="col-md-6 d-flex justify-content-center align-items-start p-0"> <!-- Removed offset and added custom class -->
                                                    <table id="BalanceSheetAssetsTable" class="balancesheetassets-table">
                                                        <thead>
                                                            <tr><th>Balance Sheet (USD Millions)</th><th>Company</th></tr>
                                                        </thead>
                                                        <tbody id="assetsBody"></tbody>
                                                    </table>
                                                </div>
                                                <div class="col-md-6 d-flex justify-content-center align-items-start p-0">
                                                    <table id="BalanceSheetLiabilitiesEquityTable" class="balancesheetliabilities-table">
                                                        <thead>
                                                            <tr><th>Balance Sheet (USD Millions)</th><th>Company</th></tr>
                                                        </thead>
                                                        <tbody id="liabilitiesBody"></tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endblock dynamic_content %}
    </section>

{% endblock %}

{% block script %}
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@4.0.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/@sgratzl/chartjs-chart-boxplot"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-matrix@2"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>

    <script>

        // ✅ Register BoxPlot and Violin controllers
        Chart.register(
            ChartBoxPlot.BoxPlotController,
            ChartBoxPlot.BoxAndWiskers,
            ChartBoxPlot.ViolinController,
            ChartDataLabels,
        );

        document.addEventListener("DOMContentLoaded", function () {
            // ✅ Show spinner on initial load
            const spinner = document.getElementById("spinner-overlay");
            if (spinner) {
                spinner.style.display = "flex";
                const loadingText = spinner.querySelector("div:last-child");
                if (loadingText) loadingText.textContent = "Loading analysis...";
            }

            let selectedValues = JSON.parse(sessionStorage.getItem("selectedValues") || "{}");
            console.log("📤 Fetching rating history with:", selectedValues);

            // Set default values if not present (for testing purposes)
            if (!selectedValues || !selectedValues.companyName || !selectedValues.sector || !selectedValues.country || !selectedValues.reportingPeriod) {
                console.warn("🚫 Missing selected values, using defaults for testing.");
                selectedValues = {
                    companyName: "Boeing Company",
                    sector: "Capital Goods",
                    country: "United States",
                    reportingPeriod: "2023/3F"
                };
                // Store the default values for consistency
                sessionStorage.setItem("selectedValues", JSON.stringify(selectedValues));
                console.log("✅ Set default values:", selectedValues);
            }

            // Create a promise array to track all fetch operations
            const fetchPromises = [];

            // Fetch rating history
            const ratingHistoryPromise = fetch(`/get_rating_history/?companyName=${encodeURIComponent(selectedValues.companyName)}&sector=${encodeURIComponent(selectedValues.sector)}&country=${encodeURIComponent(selectedValues.country)}`)
                .then(response => response.json())
                .then(data => {
                    // 🧠 Cache for later
                    sessionStorage.setItem("ratingHistory", JSON.stringify(data));
                    return data;
                })
                .catch(err => {
                    console.error("Error loading rating history:", err);
                    return [];
                });

            fetchPromises.push(ratingHistoryPromise);

            // Fetch prediction history
            const predictionHistoryPromise = fetch(`/get_prediction_history/?companyName=${encodeURIComponent(selectedValues.companyName)}`)
                .then(response => response.json())
                .then(data => {
                    console.log("✅ Prediction history received:", data);
                    if (data.success && data.predictions) {
                        // 🧠 Cache for later
                        sessionStorage.setItem("predictionHistory", JSON.stringify(data.predictions));
                        return data.predictions;
                    } else {
                        console.warn("⚠️ No prediction history available:", data.error || "Unknown error");
                        return [];
                    }
                })
                .catch(err => {
                    console.error("Error loading prediction history:", err);
                    return [];
                });

            fetchPromises.push(predictionHistoryPromise);

            // Fetch financials
            const financialsPromise = fetch(`/get_company_row_computed/?companyName=${encodeURIComponent(selectedValues.companyName)}&reportingPeriod=${encodeURIComponent(selectedValues.reportingPeriod)}&sector=${encodeURIComponent(selectedValues.sector)}`)
                .then(response => response.json())
                .then(data => {
                    if (data && data.income) {
                        sessionStorage.setItem("companyFinancials", JSON.stringify(data));
                        populateFinancialTables(data);
                    } else {
                        console.warn("⚠️ Unexpected financial data structure:", data);
                    }
                })
                .catch(err => console.error("❌ Error fetching company financial data:", err));

            fetchPromises.push(financialsPromise);

            // Fetch prediction data (from pre-computed database)
            console.log("📤 Fetching prediction data with:", selectedValues);
            const predictionPromise = fetch(`/get_prediction_data/?companyName=${encodeURIComponent(selectedValues.companyName)}&reportingPeriod=${encodeURIComponent(selectedValues.reportingPeriod)}`)
                .then(response => response.json())
                .then(data => {
                    console.log("✅ Prediction data received:", data);

                    // Store prediction data in sessionStorage
                    sessionStorage.setItem("predictionData", JSON.stringify(data));

                    if (data.success && data.prediction_label) {
                        console.log("🏆 Prediction label from database:", data.prediction_label);
                        console.log("🎯 Sector average rating:", data.sector_avg_rating);

                        // Update the main rating gauge with the predicted rating
                        updateRatingGaugeWithValue(data.prediction_label);

                        // For now, use placeholder confidence values since we don't have them in the database
                        // TODO: Add confidence values to the CapitalGoodsPrediction collection
                        updatePredictionGauges(data.prediction_label, 80, 94);
                    } else {
                        console.warn("⚠️ Prediction failed or no prediction available:", data.error || "Unknown error");
                        if (data.error_details) {
                            console.error("❌ Error details:", data.error_details);
                        }
                    }
                    return data;
                })
                .catch(err => {
                    console.error("❌ Error fetching prediction:", err);
                    return {};
                });

            fetchPromises.push(predictionPromise);

            // Wait for all fetches to complete
            Promise.all(fetchPromises)
                .then((results) => {
                    // Extract results from promises
                    const [ratingHistoryData, predictionHistoryData, financialsData, predictionDataResult] = results;

                    console.log("🎯 All data loaded:", {
                        ratingHistory: ratingHistoryData?.length || 0,
                        predictionHistory: predictionHistoryData?.length || 0,
                        financials: !!financialsData,
                        prediction: !!predictionDataResult
                    });

                    // 🎯 Plot chart with both rating history and prediction history
                    plotRatingHistoryChart(ratingHistoryData || [], predictionHistoryData || []);

                    // Process prediction data if available
                    if (predictionDataResult && predictionDataResult.success && predictionDataResult.prediction_label) {
                        console.log("✅ Processing prediction data from initial fetch:", predictionDataResult.prediction_label);

                        // Store prediction data in sessionStorage
                        sessionStorage.setItem("predictionData", JSON.stringify(predictionDataResult));

                        // Update the main rating gauge with the predicted rating
                        updateRatingGaugeWithValue(predictionDataResult.prediction_label);

                        // Update the confidence gauges with placeholder values
                        updatePredictionGauges(predictionDataResult.prediction_label, 80, 94);
                    } else {
                        console.log("🔄 No prediction data in initial fetch, falling back to historical data...");
                        // Fall back to historical data for the gauge
                        updateRatingGaugeFromData(ratingHistoryData || [], selectedValues.reportingPeriod);
                    }
                })
                .finally(() => {
                    // ✅ Hide spinner after everything is loaded
                    if (spinner) {
                        setTimeout(() => {
                            spinner.style.display = "none";
                        }, 300);
                    }
                });

            // Populate static fields
            const { companyName, reportingPeriod } = selectedValues;
            const companyNameEl = document.getElementById("companyName");
            const reportingPeriodEl = document.getElementById("reportingPeriodAndRating");
            const financialMetricsChartsEl = document.getElementById("financialMetricsCharts");
            const financialMetricsEl = document.getElementById("financialMetrics");

            if (companyNameEl && reportingPeriodEl && financialMetricsChartsEl && financialMetricsEl) {
                companyNameEl.innerText = companyName;
                reportingPeriodEl.innerText = reportingPeriod;
                financialMetricsChartsEl.innerText = `Financial Metrics: ${reportingPeriod}`;
                financialMetricsEl.innerText = `Financial Metrics: ${reportingPeriod}`;
                companyNameEl.style.visibility = "visible";
                reportingPeriodEl.style.visibility = "visible";
                financialMetricsChartsEl.style.visibility = "visible";
                financialMetricsEl.style.visibility = "visible";
            }
        });

        function updatePredictionGauges(predictionLabel, confidence, plusMinusConfidence) {
            console.log("🔄 Updating prediction gauges with:", { predictionLabel, confidence, plusMinusConfidence });

            // Update confidence gauges
            const confidenceGauge = document.getElementById("companyRatingConfidence");
            const plusMinusConfidenceGauge = document.getElementById("companyRatingPlusMinusConfidence");

            if (confidenceGauge) {
                // 🔄 Remove old canvas and text spans if present
                confidenceGauge.querySelectorAll("canvas, span, b").forEach(el => el.remove());

                // 🔧 Initialize with confidence value
                $(confidenceGauge).gaugeMeter({
                    percent: confidence,
                    text: `${confidence}%`,
                    size: 150,
                    color: "#52A0FF",
                    back: "#00000000",
                    label: "Rating|Confidence",
                    label_color: "var(--text-color1)",
                    width: 12,
                    stripe: 3,
                    animate_text_colors: true,
                    text_size: 0.24,
                });

                confidenceGauge.style.visibility = "visible";
            }

            if (plusMinusConfidenceGauge) {
                // 🔄 Remove old canvas and text spans if present
                plusMinusConfidenceGauge.querySelectorAll("canvas, span, b").forEach(el => el.remove());

                // 🔧 Initialize with plus/minus confidence value
                $(plusMinusConfidenceGauge).gaugeMeter({
                    percent: plusMinusConfidence,
                    text: `${plusMinusConfidence}%`,
                    size: 150,
                    color: "#52A0FF",
                    back: "#00000000",
                    label: "+/-1 Rating|Confidence",
                    label_color: "var(--text-color1)",
                    width: 12,
                    stripe: 3,
                    animate_text_colors: true,
                    text_size: 0.24,
                });

                plusMinusConfidenceGauge.style.visibility = "visible";
            }

            // Update the rating distribution progress bars using actual confidence values
            const downgradeBar = document.querySelector(".progress-bar-downgrade");
            const ratingBar = document.querySelector(".progress-bar-rating");
            const upgradeBar = document.querySelector(".progress-bar-upgrade");

            if (downgradeBar && ratingBar && upgradeBar) {
                // Calculate percentages based on confidence
                // The main rating gets the confidence percentage
                // The remaining percentage (100 - confidence) is split between downgrade and upgrade
                const ratingPercent = confidence;
                const remainingPercent = 100 - ratingPercent;
                const downgradePercent = Math.round(remainingPercent / 2);
                const upgradePercent = remainingPercent - downgradePercent;

                // Update the progress bars
                downgradeBar.style.width = `${downgradePercent}%`;
                downgradeBar.setAttribute("aria-valuenow", downgradePercent);
                downgradeBar.querySelector(".progress-label").textContent = `${downgradePercent}%`;

                ratingBar.style.width = `${ratingPercent}%`;
                ratingBar.setAttribute("aria-valuenow", ratingPercent);
                ratingBar.querySelector(".progress-label").textContent = `${ratingPercent}%`;

                upgradeBar.style.width = `${upgradePercent}%`;
                upgradeBar.setAttribute("aria-valuenow", upgradePercent);
                upgradeBar.querySelector(".progress-label").textContent = `${upgradePercent}%`;

                console.log("🔄 Updated rating distribution bars with values:", {
                    downgrade: downgradePercent,
                    rating: ratingPercent,
                    upgrade: upgradePercent
                });
            }
        }

        function updateRatingGaugeFromData(history, reportingPeriod) {
            // This function is now only used as a fallback when prediction data is not available
            // The main prediction logic is handled in the Promise.all section above

            const formattedQuarter = reportingPeriod.replace(/^(\d{4})\/([1-4])F$/, (_, y, q) => `Q${q} ${y}`);
            console.log("🧾 Trying to match formattedQuarter:", formattedQuarter);

            const entry = history.find(h => h.quarter === formattedQuarter);
            if (!entry) {
                console.warn(`⚠️ No rating entry found for: ${formattedQuarter}`);
                console.warn("📜 Available quarters in history:", history.map(h => h.quarter));
                return;
            }

            const rating = entry.company;
            console.log("📊 Using historical rating for gauge:", rating);
            updateRatingGaugeWithValue(rating);
        }

        function updateRatingGaugeWithValue(rating) {
            const ratingDict = {
                'AAA': 22, 'AA+': 21, 'AA': 20, 'AA-': 19, 'A+': 18, 'A': 17, 'A-': 16,
                'BBB+': 15, 'BBB': 14, 'BBB-': 13, 'BB+': 12, 'BB': 11, 'BB-': 10,
                'B+': 9, 'B': 8, 'B-': 7, 'CCC+': 6, 'CCC': 5, 'CCC-': 4,
                'CC': 3, 'C': 2, 'D': 1,
                // Add support for model prediction labels (A2, BBB2, etc.)
                'A1': 17, 'A2': 17, 'A3': 16,
                'BBB1': 15, 'BBB2': 14, 'BBB3': 13,
                'BB1': 12, 'BB2': 11, 'BB3': 10,
                'B1': 9, 'B2': 8, 'B3': 7,
                'CCC1': 6, 'CCC2': 5, 'CCC3': 4
            };

            const score = ratingDict[rating] || 1;
            const percent = (score / 22) * 100;

            const gauge = document.getElementById("companyRating");
            if (!gauge) {
                console.error("❌ Gauge element #companyRating not found in DOM.");
                return;
            }

            // 🔄 Remove old canvas and text spans if present
            gauge.querySelectorAll("canvas, span, b").forEach(el => el.remove());

            console.log("✅ Initializing gaugeMeter now with:", { rating, percent });

            // 🔧 Initialize with hardcoded config
            $(gauge).gaugeMeter({
                percent: percent.toFixed(1),
                text: rating,
                size: 150,
                color: "#52A0FF",
                back: "#00000000",
                label: "WarGame|Rating",
                label_color: "var(--text-color1)",
                width: 12,
                stripe: 3,
                animate_text_colors: true,
                text_size: 0.24,
            });

            gauge.style.visibility = "visible";
        }


        function plotRatingHistoryChart(ratingHistoryData, predictionHistoryData = []) {
            // ✅ Ensure the canvas element exists
            const ctx = document.getElementById("RatingHistory");
            if (!ctx) {
                console.error("❌ Canvas element not found!");
                return;
            }

            // ✅ Destroy any existing chart (prevents duplicate charts)
            if (window.ratingChart) {
                window.ratingChart.destroy();
            }

            // ✅ Convert rating dictionary to numeric scale (including prediction labels)
            const ratingDict = {
                'AAA': 22, 'AA+': 21, 'AA': 20, 'AA-': 19, 'A+': 18, 'A': 17, 'A-': 16,
                'BBB+': 15, 'BBB': 14, 'BBB-': 13, 'BB+': 12, 'BB': 11, 'BB-': 10,
                'B+': 9, 'B': 8, 'B-': 7, 'CCC+': 6, 'CCC': 5, 'CCC-': 4,
                'CC': 3, 'C': 2, 'D': 1,
                // Add support for model prediction labels (A2, BBB2, etc.)
                'A1': 17, 'A2': 17, 'A3': 16,
                'BBB1': 15, 'BBB2': 14, 'BBB3': 13,
                'BB1': 12, 'BB2': 11, 'BB3': 10,
                'B1': 9, 'B2': 8, 'B3': 7,
                'CCC1': 6, 'CCC2': 5, 'CCC3': 4
            };

            const mainColor = "#52A0FF";        // Blue for historical company ratings
            const secondColor = "#7FEAAF";      // Green for historical sector median
            const thirdColor = "#FF69B4";       // Pink for predictions
            const textColor = "#FAFAFA";

            // ✅ Process historical rating data
            const processedHistoryData = ratingHistoryData.map(d => ({
                quarter: d.quarter,
                company: ratingDict[d.company] || 1,
                sector: ratingDict[d.sector] || 1
            }));

            // ✅ Process prediction data - only show predictions that don't overlap with historical data
            const processedPredictionData = predictionHistoryData.map(d => ({
                quarter: d.quarter,
                prediction: ratingDict[d.prediction] || 1,
                sector_avg: ratingDict[d.sector_avg] || 1
            }));

            console.log("📊 Processed history data:", processedHistoryData);
            console.log("📊 Processed prediction data:", processedPredictionData);

            // ✅ Filter prediction data to show only recent predictions (last 5 years) and avoid overlap
            const historyQuarters = new Set(processedHistoryData.map(d => d.quarter));

            // Get the latest historical quarter to determine where predictions should start
            const latestHistoryQuarter = processedHistoryData.length > 0 ?
                processedHistoryData[processedHistoryData.length - 1].quarter : null;

            console.log("📊 Latest historical quarter:", latestHistoryQuarter);

            // Filter predictions to show only future predictions or recent ones if no future exists
            let predictionOnlyData;
            const futurePredictions = processedPredictionData.filter(d => !historyQuarters.has(d.quarter));

            if (futurePredictions.length > 0) {
                // Show only future predictions
                predictionOnlyData = futurePredictions;
                console.log("📊 Using future predictions only:", predictionOnlyData.length);
            } else {
                // No future predictions, show recent predictions (last 2 years) that don't overlap
                const currentYear = new Date().getFullYear();
                const recentPredictions = processedPredictionData.filter(d => {
                    const match = d.quarter.match(/Q\d (\d{4})/);
                    if (match) {
                        const year = parseInt(match[1]);
                        return year >= (currentYear - 2) && !historyQuarters.has(d.quarter);
                    }
                    return false;
                });
                predictionOnlyData = recentPredictions;
                console.log("📊 Using recent predictions (last 2 years):", predictionOnlyData.length);
            }

            console.log("📊 Final prediction data for chart:", predictionOnlyData);

            // ✅ Create a combined set of all quarters for the X-axis
            const allQuarters = new Set();
            processedHistoryData.forEach(d => allQuarters.add(d.quarter));
            predictionOnlyData.forEach(d => allQuarters.add(d.quarter));
            const labels = Array.from(allQuarters).sort();

            // ✅ Create data arrays aligned with the labels
            const companyRatings = labels.map(quarter => {
                const historyEntry = processedHistoryData.find(d => d.quarter === quarter);
                return historyEntry ? historyEntry.company : null;
            });

            const sectorRatings = labels.map(quarter => {
                const historyEntry = processedHistoryData.find(d => d.quarter === quarter);
                return historyEntry ? historyEntry.sector : null;
            });

            const predictionRatings = labels.map(quarter => {
                const predictionEntry = predictionOnlyData.find(d => d.quarter === quarter);
                return predictionEntry ? predictionEntry.prediction : null;
            });


            // ✅ Create the chart with only three datasets (no sector average from predictions)
            window.ratingChart = new Chart(ctx, {
                type: "line",
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: "Company Historical",
                            data: companyRatings,
                            borderColor: mainColor,
                            backgroundColor: mainColor,
                            tension: 0, // ✅ Make lines straight
                            pointRadius: 4, // ✅ Visible points
                            pointHoverRadius: 6,
                            pointBackgroundColor: mainColor, // ✅ Fully filled points
                            pointBorderColor: mainColor, // ✅ Ensures no transparency
                            pointBorderWidth: 2, // ✅ Visible border
                            fill: false, // ✅ No fill for cleaner look with multiple lines
                            spanGaps: false, // ✅ Don't connect null values
                        },
                        {
                            label: "Sector Median Historical",
                            data: sectorRatings,
                            borderColor: secondColor,
                            backgroundColor: secondColor,
                            tension: 0, // ✅ Make lines straight
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: secondColor, // ✅ Fully filled points
                            pointBorderColor: secondColor, // ✅ Ensures no transparency
                            pointBorderWidth: 2, // ✅ Visible border
                            fill: false, // ✅ No fill for cleaner look with multiple lines
                            spanGaps: false, // ✅ Don't connect null values
                        },
                        {
                            label: "Predicted Ratings",
                            data: predictionRatings,
                            borderColor: thirdColor,
                            backgroundColor: thirdColor,
                            tension: 0, // ✅ Make lines straight
                            pointRadius: 5, // ✅ Slightly larger points for predictions
                            pointHoverRadius: 7,
                            pointBackgroundColor: thirdColor, // ✅ Fully filled points
                            pointBorderColor: thirdColor, // ✅ Ensures no transparency
                            pointBorderWidth: 2, // ✅ Visible border
                            fill: false, // ✅ No fill for cleaner look
                            spanGaps: false, // ✅ Don't connect null values
                            borderDash: [5, 5], // ✅ Dashed line for predictions
                        }
                    ]
                },
                options: {
                    responsive: true,  // ✅ Ensures chart stays inside the card
                    maintainAspectRatio: false, // ✅ Allows shrinking
                    scales: {
                        y: {
                            reverse: false, // ✅ Flip Y-axis (AAA at top)
                            title: {
                                display: true,
                                text: "Rating",
                                color: textColor,
                                font: { size: 11 }
                            },
                            min: 0, // ✅ Set min to the lowest numeric value (AAA)
                            max: 23, // ✅ Set max to the highest numeric value (D)
                            ticks: {
                                stepSize: 1, // ✅ Ensures every rating appears
                                callback: function(value) {
                                    return Object.keys(ratingDict).find(key => ratingDict[key] === value) || "";
                                },
                                color: textColor,
                                font: { size: 11 }
                            },
                            grid: { display: true, color: "rgba(255,255,255,0.2)" }
                        },
                        x: {
                            ticks: { color: textColor, font: { size: 11 } },
                            grid: { display: false }
                        }
                    },
                    plugins: {
                        datalabels: { display: false },
                        legend: {
                            display: true,
                            position: "bottom", // ✅ Legend at bottom
                            labels: {
                                color: textColor,
                                font: { size: 11 },
                                usePointStyle: true, // ✅ Prevents default rectangle
                                pointStyle: 'rectRounded',
                                pointStyleWidth: 18
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.raw;
                                    if (value === null || value === undefined) {
                                        return `${context.dataset.label}: No data`;
                                    }
                                    const rating = Object.keys(ratingDict).find(key => ratingDict[key] === value);
                                    return `${context.dataset.label}: ${rating || 'Unknown'}`;
                                }
                            }
                        }
                    }
                },
            });
        }


        function populateFinancialTables(data) {
            console.log("📈 Radar chart data:", data.metrics_radar);
            console.log("📊 Populating tables with:", data);

            function buildTable(tbodyId, rows) {
                const tbody = document.getElementById(tbodyId);
                if (!tbody) return;
                tbody.innerHTML = "";

                const isIncomeTable = tbodyId === "incomeBody";
                const isCashflowTable = tbodyId === "cashflowBody";
                const isAssetTable = tbodyId === "assetsBody";
                const isLiabilityTable = tbodyId === "liabilitiesBody";
                const isThreeColumn = ["liquidityMetricsBody", "profitabilityMetricsBody", "leverageMetricsBody"].includes(tbodyId);

                // 🔹 Label style rules
                const highlightedLabels = new Set([
                    "Gross Profit", "EBIT", "PBT", "Net Income", "Funds from Operations",
                    "Net Cash from Operations", "Net Cash from Investing", "Net Cash from Financing",
                    "Net Increase (Decrease) in Cash & Equiv", "Net Incr (Decr) in Cash & Equiv"
                ]);

                const italicLabels = new Set([
                    "Sales Revenue Growth", "COGS (Excl D&A) Margin", "Gross Profit Margin",
                    "EBIT Margin", "Interest Rate", "Tax Rate", "Net Income Margin",
                    "D&A Expenses / Sales Ratio", "Capital Expenditure / Sales Ratio",
                    "Dividend Payout Ratio"
                ]);

                const assetItalicLabels = new Set(["Accounts Receivable Days", "Inventory Days"]);
                const assetHighlightedLabels = new Set(["Total Current Assets", "Net Fixed Assets", "Total Assets"]);

                const liabilityItalicLabels = new Set(["Accounts Payable Days"]);
                const liabilityHighlightedLabels = new Set([
                    "Total Current Liabilities", "Total Non Current Liabilities",
                    "Total Liabilities", "Total Shareholder Equity"
                ]);

                for (const [label, value] of Object.entries(rows)) {
                    const row = document.createElement("tr");

                    // Style logic
                    const isItalic =
                        (isIncomeTable || isCashflowTable) && italicLabels.has(label) ||
                        isAssetTable && assetItalicLabels.has(label) ||
                        isLiabilityTable && liabilityItalicLabels.has(label);

                    const isHighlight =
                        (isIncomeTable && highlightedLabels.has(label)) ||
                        (isCashflowTable && highlightedLabels.has(label) && label !== "Net Income") ||
                        (isAssetTable && assetHighlightedLabels.has(label)) ||
                        (isLiabilityTable && liabilityHighlightedLabels.has(label));

                    if (isItalic) row.classList.add("italic-row");
                    if (isHighlight) row.classList.add("highlighted-row");

                    // Create cells
                    const labelCell = document.createElement("td");
                    const companyCell = document.createElement("td");
                    const sectorCell = isThreeColumn ? document.createElement("td") : null;

                    labelCell.textContent = label;

                    // Handle 2-col vs 3-col logic
                    let companyValue = value;
                    let sectorValue = null;

                    if (isThreeColumn && typeof value === "object" && value !== null) {
                        companyValue = value.company;
                        sectorValue = value.sector;
                    }

                    const formatVal = (v, isSector = false) => {
                        const num = parseFloat(v);
                        if (isNaN(num)) return v;

                        const isPercentageMetric = (
                            (isIncomeTable || isCashflowTable) && italicLabels.has(label) ||
                            isThreeColumn && ![
                                "EBIT / Interest Expense Ratio (EBIT / IE)",
                                "Net Cash from Operations / Interest Expense Ratio (NCO / IE)",
                                "Total Assets / Total Liabilities Ratio (TA / TL)"
                            ].includes(label)
                        );

                        const isMultiplierMetric = (
                            isThreeColumn && [
                                "EBIT / Interest Expense Ratio (EBIT / IE)",
                                "Net Cash from Operations / Interest Expense Ratio (NCO / IE)",
                                "Total Assets / Total Liabilities Ratio (TA / TL)"
                            ].includes(label)
                        );

                        if (isMultiplierMetric) {
                            return `${num.toFixed(2)}x`;
                        } else if (isPercentageMetric) {
                            return `${num.toFixed(2)}%`;
                        } else {
                            return num.toLocaleString(undefined, {
                                minimumFractionDigits: 0,
                                maximumFractionDigits: 2
                            });
                        }
                    };


                    companyCell.textContent = formatVal(companyValue);
                    if (sectorCell) sectorCell.textContent = formatVal(sectorValue, true);

                    // Append to row
                    row.appendChild(labelCell);
                    row.appendChild(companyCell);
                    if (sectorCell) row.appendChild(sectorCell);
                    tbody.appendChild(row);
                }
            }

            // Build all tables
            buildTable("incomeBody", data.income || {});
            buildTable("cashflowBody", data.cashflow || {});
            buildTable("assetsBody", data.balance_assets || {});
            buildTable("liabilitiesBody", data.balance_liabilities || {});
            buildTable("liquidityMetricsBody", data.metrics?.["Liquidity"] || {});
            buildTable("profitabilityMetricsBody", data.metrics?.["Profitability / Efficiency"] || {});
            buildTable("leverageMetricsBody", data.metrics?.["Leverage / Capital Structure"] || {});

            // ✅ Call addComparisonArrows AFTER tables are built
            setTimeout(addComparisonArrows, 10);

            if (data.metrics_radar) {
                drawRadarChart("liquidityRadar", normalizeData(data.metrics_radar.liquidity));
                drawRadarChart("profitabilityRadar", normalizeData(data.metrics_radar.profitability));
                drawRadarChart("leverageRadar", normalizeData(data.metrics_radar.leverage));
            }
        }

        function normalizeData(dataList) {
            const percentageKeywords = [
                "WC / TA", "Cash / TA", "CuR", "CaR", "SRG", "GPM", "EBITM", "NIM", "Sales / TA",
                "EBIT / TA", "EBIT / ND", "NCO / ND", "FCF / ND", "Equity / TA", "Equity / TL"
            ];

            const multiplierKeywords = [
                "EBIT / IE", "NCO / IE", "TA / TL"
            ];

            const normalized = dataList.map(d => {
                const companyVal = parseFloat(d.column2);
                const sectorVal = parseFloat(d.column3);

                const isCompanyMax = companyVal >= sectorVal;
                const isSectorMax = sectorVal >= companyVal;

                let format = ""; // Default format

                if (percentageKeywords.some(key => d.section.includes(key))) {
                    format = "%";
                } else if (multiplierKeywords.some(key => d.section.includes(key))) {
                    format = "x";
                }

                return {
                    section: d.section,
                    format: format,
                    companyOriginal: companyVal,
                    sectorOriginal: sectorVal,
                    column2: isCompanyMax ? 0.8 : 0.2,
                    column3: isSectorMax ? 0.8 : 0.2
                };
            });

            return {
                labels: normalized.map(d => d.section),
                normalized,
                datasets: [
                    {
                        label: "Company",
                        data: normalized.map(d => d.column2),
                        original: normalized.map(d => d.companyOriginal),
                        format: normalized.map(d => d.format),
                        fill: true,
                        borderWidth: 1
                    },
                    {
                        label: "Sector Median",
                        data: normalized.map(d => d.column3),
                        original: normalized.map(d => d.sectorOriginal),
                        format: normalized.map(d => d.format),
                        fill: true,
                        borderDash: [2, 2],
                        borderWidth: 1
                    }
                ]
            };
        }


        function drawRadarChart(containerId, data) {
            const mainColor = "#52A0FF";
            const secondColor = "#7FEAAF";
            const textColor = "#FAFAFA";

            const marginRadarChart = { top: 10, right: 20, bottom: 20, left: 20 };
            const width = 160;
            const height = 160;
            const color = d3.scaleOrdinal().range([mainColor, secondColor]);
            const radians = 2 * Math.PI;
            const centerX = width / 2 + marginRadarChart.left;
            const centerY = height / 2 + marginRadarChart.top;
            const radius = d3.scaleLinear().range([0, Math.min(width, height) / 2]).domain([0, 1]);

            // Create SVG container
            const svg = d3.select(`#${containerId}`).append('svg')
                .attr('width', width + marginRadarChart.left + marginRadarChart.right)
                .attr('height', height + marginRadarChart.top + marginRadarChart.bottom)
                .append('g')
                .attr('transform', `translate(${centerX},${centerY})`);

            // Create tooltip div
            const tooltip = d3.select("body").append("div")
                .attr("class", "tooltip")
                .style("position", "absolute")
                .style("background-color", "#222")
                .style("color", textColor)
                .style("padding", "5px")
                .style("border-radius", "5px")
                .style("box-shadow", "0px 0px 5px rgba(0,0,0,0.3)")
                .style("pointer-events", "none")
                .style("opacity", 0);

            // Radar grid circles
            const ticks = d3.range(0, 0.81, 0.2);
            svg.selectAll('.circle-ticks')
                .data(ticks)
                .enter().append('circle')
                .attr('class', 'circle')
                .attr('r', d => radius(d))
                .attr('stroke', 'rgba(255,255,255,0.5)')
                .attr('fill', 'none');

            // Radar axes (spokes)
            const lineAxes = svg.selectAll('.line-ticks')
                .data(data.labels)
                .enter().append('g')
                .attr('transform', (d, i) => `rotate(${(i / data.labels.length * 360) - 90})translate(${radius(1)})`)
                .attr('class', 'line-ticks');

            lineAxes.append('line')
                .attr('x2', -radius(1))
                .style('stroke', 'rgba(255,255,255,0.3)')
                .style('fill', 'none');

            lineAxes.append('text')
                .text(d => d)
                .attr('text-anchor', 'middle')
                .attr('transform', (d, i) => `rotate(${90 - (i * 360 / data.labels.length)})`)
                .attr('dy', '0.3em')
                .style('fill', textColor)
                .style('font-size', '10px');

            // Prepare radar data (already normalized)
            const radarData = [
                data.datasets[0].data,  // Sector Median
                data.datasets[1].data   // Company
            ];

            function drawRadarArea(className, radarValues, fillColor) {
                svg.selectAll(`.${className}`)
                    .data([radarValues])
                    .enter().append('path')
                    .attr('class', className)
                    .attr('d', d3.radialLine()
                        .radius((d, i) => radius(d))
                        .angle((d, i) => (i / data.labels.length) * radians)
                        .curve(d3.curveCardinalClosed))
                    .style('fill', fillColor)
                    .style('fill-opacity', 0.4)
                    .style('stroke', fillColor)
                    .style('stroke-width', 2);
            }

            drawRadarArea('radar-area-bg', radarData[0], color(0));
            drawRadarArea('radar-area-fg', radarData[1], color(1));

            function drawRadarPoints(className, radarValues, pointColor, isCompany) {
                svg.selectAll(`.${className}`)
                    .data([radarValues])
                    .enter().append('g')
                    .attr('class', className)
                    .selectAll('.point')
                    .data((d, i) => d.map((value, idx) => {
                        const norm = data.normalized[idx];
                        return {
                            value,
                            idx,
                            original: isCompany ? norm.companyOriginal : norm.sectorOriginal,
                            format: norm.format
                        };
                    }))
                    .enter().append('circle')
                    .attr('class', 'point')
                    .attr('cx', d => radius(d.value) * Math.cos((d.idx / data.labels.length) * radians - Math.PI / 2))
                    .attr('cy', d => radius(d.value) * Math.sin((d.idx / data.labels.length) * radians - Math.PI / 2))
                    .attr('r', 3)
                    .style('fill', pointColor)
                    .style('stroke', 'none')
                    .on("mouseover", function (event, d) {
                        tooltip.transition().duration(200).style("opacity", 1);

                        let val = d.original.toFixed(2);
                        if (d.format === "%") val += "%";
                        else if (d.format === "x") val += "x";

                        tooltip.html(`${data.labels[d.idx]}: ${val}`)
                            .style("left", (event.pageX + 10) + "px")
                            .style("top", (event.pageY - 20) + "px");

                        d3.select(this).transition().duration(200).attr("r", 6);
                    })
                    .on("mouseout", function () {
                        tooltip.transition().duration(500).style("opacity", 0);
                        d3.select(this).transition().duration(200).attr("r", 3);
                    });
            }

            drawRadarPoints('radar-point-fg', radarData[0], color(0), true);  // Company
            drawRadarPoints('radar-point-bg', radarData[1], color(1), false); // Sector
        }

        function addComparisonArrows() {
            document.querySelectorAll('.financial-metrics-table tbody tr').forEach(function (row) {
                const companyCell = row.cells[1];
                const sectorCell = row.cells[2];

                if (!companyCell || !sectorCell) return;

                // Extract raw values (ignoring % and x suffixes)
                const companyRaw = companyCell.childNodes[0]?.nodeValue || '';
                const sectorRaw = sectorCell.childNodes[0]?.nodeValue || '';

                const companyValue = parseFloat(companyRaw.replace(/[,%x]/g, ""));
                const sectorValue = parseFloat(sectorRaw.replace(/[,%x]/g, ""));

                if (isNaN(companyValue) || isNaN(sectorValue)) return;

                // Decide which arrow and color to use
                let iconClass = "";
                let arrowColor = "";

                if (companyValue > sectorValue) {
                    iconClass = "bi-arrow-up-square";
                    arrowColor = "#7FEAAF"; // Up
                } else if (companyValue < sectorValue) {
                    iconClass = "bi-arrow-down-square";
                    arrowColor = "#B152FF"; // Down
                } else {
                    iconClass = "bi-arrow-right-square";
                    arrowColor = "#52A0FF"; // Flat
                }

                // Create a wrapper span for layout control
                const wrapper = document.createElement("span");
                wrapper.style.display = "inline-flex";
                wrapper.style.alignItems = "center";

                // Create a span for the number
                const valueSpan = document.createElement("span");
                valueSpan.textContent = companyRaw;

                // Create the arrow icon
                const icon = document.createElement("i");
                icon.classList.add("bi", iconClass, "arrow-icon");
                icon.style.color = arrowColor;
                icon.style.marginLeft = "5px";

                // Append both number and arrow icon into wrapper
                wrapper.appendChild(valueSpan);
                wrapper.appendChild(icon);

                // Replace the companyCell content with wrapper
                companyCell.innerHTML = "";
                companyCell.appendChild(wrapper);
            });
        }

        document.addEventListener("DOMContentLoaded", function() {
            function plotSpreadTermStructureChart(data) {
                // ✅ Ensure the canvas element exists
                const ctx = document.getElementById("SpreadTermStructure");
                if (!ctx) {
                    console.error("❌ Canvas element not found!");
                    return;
                }

                // ✅ Destroy any existing chart (prevents duplicate charts)
                if (window.spreadChart) {
                    window.spreadChart.destroy();
                }

                // ✅ Define colors
                const mainColor = "#52A0FF";
                const secondColor = "#7FEAAF";
                const thirdColor = "#B152FF";
                const textColor = "#FAFAFA";
                const mainColorRGB = "82, 150, 255";
                const secondColorRGB = "127, 234, 175";

                // ✅ Create gradient fill
                const ctx2d = ctx.getContext("2d");
                const gradientSpread = ctx2d.createLinearGradient(0, 0, 0, ctx.height);
                gradientSpread.addColorStop(0, `rgba(${mainColorRGB}, 0.6)`); // Light green at top
                gradientSpread.addColorStop(1, `rgba(${mainColorRGB}, 0.1)`); // Transparent at bottom

                // ✅ Extract X (maturity) and Y (spread) values
                const labels = data.map(d => d.maturity);
                const spreads = data.map(d => d.spread);

                // ✅ Create the chart
                window.spreadChart = new Chart(ctx, {
                    type: "line",
                    data: {
                        labels: labels,
                        datasets: [
                            {
                                label: "CDS Spread",
                                data: spreads,
                                borderColor: mainColor,
                                backgroundColor: mainColor,
                                tension: 0, // ✅ Make lines straight
                                pointRadius: 4,
                                pointHoverRadius: 6,
                                pointBackgroundColor: mainColor, // ✅ Fully filled points
                                pointBorderColor: mainColor, // ✅ Ensures no transparency
                                pointBorderWidth: 2, // ✅ Visible border
                                fill: true,
                                backgroundColor: gradientSpread, // ✅ Gradient Fill
                            }
                        ]
                    },
                    options: {
                        responsive: true,  // ✅ Ensures chart stays inside the card
                        maintainAspectRatio: false, // ✅ Allows shrinking
                        scales: {
                            y: {
                                title: {
                                    display: true,
                                    text: "CDS Spread (bps)",
                                    color: textColor,
                                    font: { size: 11 }
                                },
                                min: 0, // ✅ Ensure the bottom starts at 0
                                max: Math.max(...spreads) + 20, // ✅ Dynamic max limit
                                ticks: {
                                    color: textColor,
                                    font: { size: 11 }
                                },
                                grid: { display: true, color: "rgba(255,255,255,0.2)" }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: "Maturity (Years)",
                                    color: textColor,
                                    font: { size: 11 }
                                },
                                type: 'linear', // ✅ Ensures a continuous numeric scale
                                min: 0, // ✅ Explicitly set min to 0
                                suggestedMin: 0, // ✅ Ensures the scale does not shrink below 0
                                ticks: { color: textColor, font: { size: 11 } },
                                grid: { display: false }
                            }
                        },
                        plugins: {
                            datalabels: { display: false },
                            legend: {
                                display: false,
                                position: "bottom",
                                labels: {
                                    color: textColor,
                                    font: { size: 11 },
                                    usePointStyle: true, // ✅ Custom legend style
                                    pointStyle: 'rectRounded',
                                    padding: 0 // ✅ Increases horizontal spacing
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    title: function(tooltipItems) {
                                        const xValue = tooltipItems[0].label; // ✅ Get maturity (years)
                                        return `${xValue} Years`; // ✅ Display it as the title
                                    },
                                    label: function(context) {
                                        const yValue = context.raw; // ✅ Get spread (bps)
                                        return `Spread: ${yValue} bps`; // ✅ Display spread on a new line
                                    }
                                }
                            }
                        }
                    },
                });
            }

            // ✅ Sample Data
            const spreadData = [
                { maturity: 0.2, spread: 30 },
                { maturity: 0.5, spread: 60 },
                { maturity: 1, spread: 80 },
                { maturity: 3, spread: 120 },
                { maturity: 5, spread: 140 },
                { maturity: 7, spread: 160 },
                { maturity: 10, spread: 180 }
            ];

            // ✅ Call the function to plot the chart
            plotSpreadTermStructureChart(spreadData);
        });


        document.addEventListener("DOMContentLoaded", function () {
            // Retrieve stored values
            const savedValues = JSON.parse(sessionStorage.getItem("selectedValues"));

            if (savedValues) {
                const { companyName, reportingPeriod } = savedValues;

                // ✅ Get elements
                const companyNameEl = document.getElementById("companyName");
                const reportingPeriodEl = document.getElementById("reportingPeriodAndRating");
                const financialMetricsChartsEl = document.getElementById("financialMetricsCharts");
                const financialMetricsEl = document.getElementById("financialMetrics");

                // ✅ Update text content
                companyNameEl.innerText = companyName;
                reportingPeriodEl.innerText = reportingPeriod;
                financialMetricsChartsEl.innerText = `Financial Metrics: ${reportingPeriod}`;
                financialMetricsEl.innerText = `Financial Metrics: ${reportingPeriod}`;

                // ✅ Make elements visible
                companyNameEl.style.visibility = "visible";
                reportingPeriodEl.style.visibility = "visible";
                financialMetricsChartsEl.style.visibility = "visible";
                financialMetricsEl.style.visibility = "visible";
            }
        });
    </script>


{% endblock %}